#!/usr/bin/env python3
"""
Enhanced Document Parser with LLM Integration
Supports various document types including printed invoices, digital receipts, and handwritten documents.
Uses LLM for semantic understanding and Pydantic for data validation.
"""

import os
import sys
import argparse
import logging
import csv
import json
from pathlib import Path
from typing import List, Optional
import time

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("python-dotenv not installed. Install with: pip install python-dotenv")
    print("Or set GROQ_API_KEY as system environment variable")

# Import our new parser
try:
    from parser import DocumentParser
    from models import InvoiceData, DocumentParsingResult
    ENHANCED_PARSER_AVAILABLE = True
except ImportError as e:
    print(f"Enhanced parser not available: {e}")
    print("Please install required dependencies: pip install groq instructor pydantic pytesseract pillow pymupdf")
    ENHANCED_PARSER_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def setup_argument_parser() -> argparse.ArgumentParser:
    """Set up command line argument parser."""
    parser = argparse.ArgumentParser(
        description="Enhanced Document Parser with LLM Integration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python enhanced_test.py --input invoice.pdf --output result.csv
  python enhanced_test.py --input receipts/ --output batch_results.csv --batch
  python enhanced_test.py --input document.jpg --output parsed.csv --verbose
        """
    )
    
    parser.add_argument(
        '--input', '-i',
        type=str,
        required=True,
        help='Input file or directory path'
    )
    
    parser.add_argument(
        '--output', '-o',
        type=str,
        required=True,
        help='Output CSV file path'
    )
    
    parser.add_argument(
        '--batch', '-b',
        action='store_true',
        help='Process all files in input directory (batch mode)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    parser.add_argument(
        '--groq-api-key',
        type=str,
        help='Groq API key (can also be set via GROQ_API_KEY environment variable)'
    )
    
    parser.add_argument(
        '--save-json',
        action='store_true',
        help='Also save results in JSON format'
    )
    
    parser.add_argument(
        '--save-raw-text',
        action='store_true',
        help='Save extracted raw text'
    )
    
    return parser


def find_files_to_process(input_path: Path, batch_mode: bool) -> List[Path]:
    """Find files to process based on input path and mode."""
    supported_extensions = {'.pdf', '.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.txt'}
    
    if batch_mode:
        if not input_path.is_dir():
            raise ValueError(f"Batch mode requires a directory, got: {input_path}")
        
        files = []
        for ext in supported_extensions:
            files.extend(input_path.glob(f"*{ext}"))
            files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not files:
            raise ValueError(f"No supported files found in directory: {input_path}")
        
        return sorted(files)
    else:
        if not input_path.exists():
            raise ValueError(f"Input file not found: {input_path}")
        
        if input_path.suffix.lower() not in supported_extensions:
            raise ValueError(f"Unsupported file type: {input_path.suffix}")
        
        return [input_path]


def save_results_to_csv(results: List[DocumentParsingResult], output_path: Path) -> None:
    """Save parsing results to CSV file."""
    csv_rows = []
    
    for result in results:
        if result.success and result.invoice_data:
            # Convert invoice data to CSV rows
            invoice_rows = result.invoice_data.to_csv_rows()
            csv_rows.extend(invoice_rows)
        else:
            # Add error row
            csv_rows.append({
                'invoice_number': '',
                'invoice_date': '',
                'vendor_name': '',
                'customer_name': '',
                'currency': '',
                'subtotal': 0,
                'tax_amount': 0,
                'total': 0,
                'item_name': f"ERROR: {result.error_message}",
                'quantity': 0,
                'unit_price': 0,
                'item_total': 0
            })
    
    if not csv_rows:
        logger.warning("No data to save to CSV")
        return
    
    # Write CSV file
    with open(output_path, 'w', newline='', encoding='utf-8') as f:
        if csv_rows:
            writer = csv.DictWriter(f, fieldnames=csv_rows[0].keys())
            writer.writeheader()
            writer.writerows(csv_rows)
    
    logger.info(f"Results saved to: {output_path}")


def save_results_to_json(results: List[DocumentParsingResult], output_path: Path) -> None:
    """Save parsing results to JSON file."""
    json_data = []
    
    for result in results:
        result_dict = {
            'success': result.success,
            'error_message': result.error_message,
            'processing_time': result.processing_time,
            'extraction_method': result.extraction_method,
            'invoice_data': result.invoice_data.dict() if result.invoice_data else None
        }
        json_data.append(result_dict)
    
    json_path = output_path.with_suffix('.json')
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, indent=2, ensure_ascii=False, default=str)
    
    logger.info(f"JSON results saved to: {json_path}")


def save_raw_text(results: List[DocumentParsingResult], output_path: Path) -> None:
    """Save extracted raw text files."""
    for i, result in enumerate(results):
        if result.raw_text:
            text_path = output_path.parent / f"{output_path.stem}_raw_text_{i}.txt"
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(result.raw_text)
            logger.info(f"Raw text saved to: {text_path}")


def main():
    """Main function."""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # Set up logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate enhanced parser availability
    if not ENHANCED_PARSER_AVAILABLE:
        logger.error("Enhanced parser not available. Please install required dependencies.")
        sys.exit(1)
    
    # Set up API key
    groq_api_key = args.groq_api_key or os.getenv("GROQ_API_KEY")
    if not groq_api_key:
        logger.error("GROQ_API_KEY not found. Please set it as environment variable or use --groq-api-key")
        sys.exit(1)
    
    try:
        # Initialize parser
        doc_parser = DocumentParser(groq_api_key=groq_api_key)
        
        # Find files to process
        input_path = Path(args.input)
        files_to_process = find_files_to_process(input_path, args.batch)
        
        logger.info(f"Found {len(files_to_process)} file(s) to process")
        
        # Process files
        start_time = time.time()
        if args.batch:
            results = doc_parser.batch_parse(files_to_process)
        else:
            result = doc_parser.parse_document(files_to_process[0])
            results = [result]
        
        total_time = time.time() - start_time
        
        # Results are ready for CSV output
        
        # Save results
        output_path = Path(args.output)
        save_results_to_csv(results, output_path)
        
        if args.save_json:
            save_results_to_json(results, output_path)
        
        if args.save_raw_text:
            save_raw_text(results, output_path)
        
        # Print summary
        successful = sum(1 for r in results if r.success)
        failed = len(results) - successful
        
        print(f"\n{'='*60}")
        print(f"PROCESSING COMPLETE")
        print(f"{'='*60}")
        print(f"Total files processed: {len(results)}")
        print(f"Successful: {successful}")
        print(f"Failed: {failed}")
        print(f"Total processing time: {total_time:.2f} seconds")
        print(f"Average time per file: {total_time/len(results):.2f} seconds")
        print(f"Results saved to: {output_path}")
        
        if failed > 0:
            print(f"\nFailed files:")
            for i, result in enumerate(results):
                if not result.success:
                    file_path = files_to_process[i] if i < len(files_to_process) else "unknown"
                    print(f"  - {file_path}: {result.error_message}")
    
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
