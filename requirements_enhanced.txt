# Enhanced Document Parser Requirements

# Core dependencies
pydantic>=2.0.0
groq>=0.4.0
instructor>=0.4.0

# OCR and image processing
pytesseract>=0.3.10
Pillow>=9.0.0
PyMuPDF>=1.23.0

# Optional: For better image processing
pdf2image>=3.1.0

# Existing dependencies (keep for fallback)
docling>=1.0.0
transformers>=4.30.0
torch>=2.0.0

# Development and testing
pytest>=7.0.0
black>=23.0.0
flake8>=6.0.0
