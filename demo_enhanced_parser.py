#!/usr/bin/env python3
"""
Demo script for the Enhanced Document Parser with LLM Integration.
Shows various usage examples and validates functionality.
"""

import os
import sys
from pathlib import Path
import time
import json

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("python-dotenv not installed. Install with: pip install python-dotenv")
    print("Or set GROQ_API_KEY as system environment variable")

# Import our enhanced parser
try:
    from parser import DocumentParser
    from models import InvoiceData, DocumentParsingResult
    from batch_processor import BatchProcessor
    ENHANCED_PARSER_AVAILABLE = True
except ImportError as e:
    print(f"Enhanced parser not available: {e}")
    print("Please install required dependencies:")
    print("pip install groq instructor pydantic pytesseract pillow pymupdf")
    ENHANCED_PARSER_AVAILABLE = False
    sys.exit(1)


def demo_single_file_parsing():
    """Demo single file parsing."""
    print("\n" + "="*60)
    print("DEMO: Single File Parsing")
    print("="*60)
    
    # Check for API key
    groq_api_key = os.getenv("GROQ_API_KEY")
    if not groq_api_key:
        print("❌ GROQ_API_KEY not found. Please set it as environment variable.")
        return
    
    # Initialize parser
    parser = DocumentParser(groq_api_key)
    
    # Look for test files
    test_files = []
    possible_locations = [Path("."), Path("Data"), Path("../Data")]
    
    for location in possible_locations:
        if location.exists():
            test_files.extend(location.glob("*.pdf"))
            test_files.extend(location.glob("*.jpg"))
            test_files.extend(location.glob("*.png"))
    
    if not test_files:
        print("❌ No test files found. Please add some PDF/image files to test.")
        return
    
    # Test with first available file
    test_file = test_files[0]
    print(f"📄 Testing with: {test_file}")
    
    start_time = time.time()
    result = parser.parse_document(test_file)
    processing_time = time.time() - start_time
    
    if result.success:
        print(f"✅ Successfully parsed in {processing_time:.2f} seconds")
        print(f"📊 Extraction method: {result.extraction_method}")
        
        if result.invoice_data:
            invoice = result.invoice_data
            print(f"💰 Total: {invoice.total} {invoice.currency}")
            print(f"📅 Date: {invoice.invoice_date}")
            print(f"🏢 Vendor: {invoice.vendor.name if invoice.vendor else 'N/A'}")
            print(f"📦 Items: {len(invoice.items)}")
            
            if invoice.items:
                print("\n📋 Line Items:")
                for i, item in enumerate(invoice.items[:3], 1):  # Show first 3 items
                    print(f"  {i}. {item.name} - ${item.total_price}")
                if len(invoice.items) > 3:
                    print(f"  ... and {len(invoice.items) - 3} more items")
        
        # Save demo output
        output_dir = Path("demo_output")
        output_dir.mkdir(exist_ok=True)
        
        # Save CSV
        csv_path = output_dir / f"{test_file.stem}_demo.csv"
        parser.export_to_csv([result], csv_path)
        print(f"💾 CSV saved to: {csv_path}")
        
        # Save JSON
        if result.invoice_data:
            json_path = output_dir / f"{test_file.stem}_demo.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(result.invoice_data.dict(), f, indent=2, default=str)
            print(f"💾 JSON saved to: {json_path}")
    
    else:
        print(f"❌ Failed to parse: {result.error_message}")


def demo_batch_processing():
    """Demo batch processing."""
    print("\n" + "="*60)
    print("DEMO: Batch Processing")
    print("="*60)
    
    # Check for API key
    groq_api_key = os.getenv("GROQ_API_KEY")
    if not groq_api_key:
        print("❌ GROQ_API_KEY not found. Please set it as environment variable.")
        return
    
    # Find test files
    test_files = []
    possible_locations = [Path("."), Path("Data"), Path("../Data")]
    
    for location in possible_locations:
        if location.exists():
            test_files.extend(location.glob("*.pdf"))
            test_files.extend(location.glob("*.jpg"))
            test_files.extend(location.glob("*.png"))
    
    if len(test_files) < 2:
        print("❌ Need at least 2 test files for batch processing demo.")
        return
    
    # Limit to first 3 files for demo
    test_files = test_files[:3]
    print(f"📁 Testing batch processing with {len(test_files)} files:")
    for file in test_files:
        print(f"  - {file.name}")
    
    # Initialize batch processor
    processor = BatchProcessor(groq_api_key, max_workers=2)
    
    # Process files
    start_time = time.time()
    results = processor.process_batch_parallel(test_files)
    total_time = time.time() - start_time
    
    # Generate report
    report = processor.generate_batch_report(results)
    summary = report['summary']
    
    print(f"\n📊 Batch Processing Results:")
    print(f"  Total files: {summary['total_files']}")
    print(f"  Successful: {summary['successful']}")
    print(f"  Failed: {summary['failed']}")
    print(f"  Success rate: {summary['success_rate']:.1f}%")
    print(f"  Total time: {total_time:.2f} seconds")
    print(f"  Avg time per file: {summary['average_processing_time']:.2f} seconds")
    
    # Save batch results
    output_dir = Path("demo_output/batch_demo")
    processor.save_batch_results(results, output_dir)
    print(f"💾 Batch results saved to: {output_dir}")


def demo_data_validation():
    """Demo Pydantic data validation."""
    print("\n" + "="*60)
    print("DEMO: Data Validation with Pydantic")
    print("="*60)
    
    # Test data validation
    from models import InvoiceData, LineItem, ContactInfo
    
    print("🔍 Testing data validation and normalization...")
    
    # Test 1: Valid data
    try:
        invoice = InvoiceData(
            total=150.75,
            invoice_date="2024-01-15",
            vendor=ContactInfo(name="Test Vendor", email="<EMAIL>"),
            items=[
                LineItem(name="test item", total_price=100.50),
                LineItem(name="another item", quantity=2, unit_price=25.125, total_price=50.25)
            ]
        )
        print("✅ Valid invoice data created successfully")
        print(f"  Total: ${invoice.total}")
        print(f"  Date: {invoice.invoice_date}")
        print(f"  Items: {len(invoice.items)}")
    except Exception as e:
        print(f"❌ Validation error: {e}")
    
    # Test 2: Data cleaning and normalization
    try:
        messy_item = LineItem(
            name="  MESSY   item   NAME  ",  # Extra whitespace
            total_price=99.999  # Will be rounded
        )
        print(f"✅ Data cleaning: '{messy_item.name}' -> Price: ${messy_item.total_price}")
    except Exception as e:
        print(f"❌ Cleaning error: {e}")
    
    # Test 3: Invalid data handling
    try:
        invalid_invoice = InvoiceData(
            total=-50,  # Negative total should fail
            items=[]
        )
        print("❌ Should have failed validation!")
    except Exception as e:
        print(f"✅ Correctly caught invalid data: {e}")


def demo_csv_export():
    """Demo CSV export functionality."""
    print("\n" + "="*60)
    print("DEMO: CSV Export")
    print("="*60)
    
    from models import InvoiceData, LineItem, ContactInfo, TaxInfo
    
    # Create sample invoice data
    sample_invoice = InvoiceData(
        invoice_number="INV-2024-001",
        invoice_date="2024-01-15",
        vendor=ContactInfo(
            name="Demo Vendor Inc.",
            address="123 Business St, City, State 12345",
            email="<EMAIL>"
        ),
        customer=ContactInfo(
            name="John Doe",
            address="456 Customer Ave, Town, State 67890"
        ),
        subtotal=100.00,
        tax_info=TaxInfo(
            tax_type="Sales Tax",
            tax_rate=8.5,
            tax_amount=8.50
        ),
        total=108.50,
        currency="USD",
        items=[
            LineItem(name="Product A", quantity=2, unit_price=25.00, total_price=50.00),
            LineItem(name="Product B", quantity=1, unit_price=30.00, total_price=30.00),
            LineItem(name="Service Fee", quantity=1, unit_price=20.00, total_price=20.00)
        ]
    )
    
    # Convert to CSV format
    csv_rows = sample_invoice.to_csv_rows()
    
    print(f"📊 Generated {len(csv_rows)} CSV rows:")
    print("  Columns:", list(csv_rows[0].keys()) if csv_rows else "None")
    
    # Save to file
    output_dir = Path("demo_output")
    output_dir.mkdir(exist_ok=True)
    
    csv_path = output_dir / "sample_invoice_demo.csv"
    import csv
    with open(csv_path, 'w', newline='', encoding='utf-8') as f:
        if csv_rows:
            writer = csv.DictWriter(f, fieldnames=csv_rows[0].keys())
            writer.writeheader()
            writer.writerows(csv_rows)
    
    print(f"💾 Sample CSV saved to: {csv_path}")


def main():
    """Run all demos."""
    print("🚀 Enhanced Document Parser Demo")
    print("=" * 60)
    
    if not ENHANCED_PARSER_AVAILABLE:
        print("❌ Enhanced parser not available. Please install dependencies.")
        return
    
    # Check API key
    if not os.getenv("GROQ_API_KEY"):
        print("⚠️  GROQ_API_KEY not set. Some demos will be skipped.")
        print("   Set your API key: export GROQ_API_KEY='your-key-here'")
    
    try:
        # Run demos
        demo_data_validation()
        demo_csv_export()
        
        if os.getenv("GROQ_API_KEY"):
            demo_single_file_parsing()
            demo_batch_processing()
        else:
            print("\n⚠️  Skipping LLM-based demos (no API key)")
        
        print("\n" + "="*60)
        print("✅ Demo completed successfully!")
        print("📁 Check the 'demo_output' directory for generated files.")
        print("="*60)
    
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
