#!/usr/bin/env python3
"""
Setup script for Enhanced Document Parser.
Helps with installation and configuration.
"""

import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True


def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    
    requirements_file = Path("requirements_enhanced.txt")
    if not requirements_file.exists():
        print("❌ requirements_enhanced.txt not found")
        return False
    
    try:
        # Install core dependencies first
        core_deps = [
            "python-dotenv>=1.0.0",
            "pydantic>=2.0.0", 
            "groq>=0.4.0",
            "instructor>=0.4.0",
            "pytesseract>=0.3.10",
            "Pillow>=9.0.0",
            "PyMuPDF>=1.23.0"
        ]
        
        for dep in core_deps:
            print(f"Installing {dep}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print(f"⚠️  Warning: Failed to install {dep}")
                print(f"   Error: {result.stderr}")
            else:
                print(f"✅ Installed {dep}")
        
        return True
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False


def setup_env_file():
    """Set up .env file."""
    print("\n🔧 Setting up environment file...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if not env_example.exists():
        print("❌ .env.example not found")
        return False
    
    # Copy example to .env
    try:
        with open(env_example, 'r') as f:
            content = f.read()
        
        with open(env_file, 'w') as f:
            f.write(content)
        
        print("✅ Created .env file from template")
        print("📝 Please edit .env file and add your GROQ_API_KEY")
        return True
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
        return False


def check_tesseract():
    """Check if Tesseract is available."""
    print("\n🔍 Checking Tesseract OCR...")
    
    try:
        import pytesseract
        from PIL import Image
        
        # Try to get Tesseract version
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract version: {version}")
        return True
    except Exception as e:
        print(f"⚠️  Tesseract not found or not configured: {e}")
        print("   Please install Tesseract OCR:")
        print("   - Windows: https://github.com/UB-Mannheim/tesseract/wiki")
        print("   - macOS: brew install tesseract")
        print("   - Linux: sudo apt-get install tesseract-ocr")
        return False


def test_groq_connection():
    """Test Groq API connection."""
    print("\n🌐 Testing Groq API connection...")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv("GROQ_API_KEY")
        if not api_key or api_key == "your-groq-api-key-here":
            print("⚠️  GROQ_API_KEY not set in .env file")
            print("   Please edit .env file and add your API key")
            print("   Get your key from: https://console.groq.com/")
            return False
        
        # Test API connection
        from groq import Groq
        client = Groq(api_key=api_key)
        
        # Simple test call
        response = client.chat.completions.create(
            model="llama-3.3-70b-versatile",
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=10
        )
        
        print("✅ Groq API connection successful")
        return True
    except Exception as e:
        print(f"❌ Groq API test failed: {e}")
        print("   Please check your API key in .env file")
        return False


def run_demo():
    """Run a quick demo."""
    print("\n🚀 Running demo...")
    
    try:
        result = subprocess.run([sys.executable, "demo_enhanced_parser.py"], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Demo completed successfully")
            print("📁 Check 'demo_output' directory for results")
            return True
        else:
            print(f"❌ Demo failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("⚠️  Demo timed out")
        return False
    except Exception as e:
        print(f"❌ Error running demo: {e}")
        return False


def main():
    """Main setup function."""
    print("🚀 Enhanced Document Parser Setup")
    print("=" * 50)
    
    success_count = 0
    total_steps = 5
    
    # Step 1: Check Python version
    if check_python_version():
        success_count += 1
    
    # Step 2: Install dependencies
    if install_dependencies():
        success_count += 1
    
    # Step 3: Setup .env file
    if setup_env_file():
        success_count += 1
    
    # Step 4: Check Tesseract
    if check_tesseract():
        success_count += 1
    
    # Step 5: Test Groq connection (optional)
    if test_groq_connection():
        success_count += 1
    
    print(f"\n📊 Setup Summary: {success_count}/{total_steps} steps completed")
    
    if success_count >= 3:
        print("\n✅ Setup completed successfully!")
        print("\n🎯 Next steps:")
        print("1. Edit .env file and add your GROQ_API_KEY")
        print("2. Test with: python enhanced_test.py --input your_document.pdf --output results.csv")
        print("3. Run demo: python demo_enhanced_parser.py")
        
        # Optionally run demo
        if success_count == total_steps:
            response = input("\n🤔 Would you like to run the demo now? (y/n): ")
            if response.lower() in ['y', 'yes']:
                run_demo()
    else:
        print("\n⚠️  Setup incomplete. Please resolve the issues above.")
    
    print("\n📚 For more information, see README_enhanced.md")


if __name__ == "__main__":
    main()
