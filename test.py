from pathlib import Path
import difflib
import re
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import (
    PdfPipelineOptions,
    TesseractCliOcrOptions,
)
from docling.document_converter import DocumentConverter, PdfFormatOption
ocr_options = TesseractCliOcrOptions(
    lang=["auto"],
    tesseract_cmd=r"C:\Users\<USER>\Documents\Intern_2025\ArunKumarV(2951795)\Tesseractengine\tesseract.exe"
)

def normalize_text(text):
    """Normalize text for comparison by removing extra whitespace and converting to lowercase."""
    # Remove extra whitespace and normalize
    text = re.sub(r'\s+', ' ', text.strip())
    return text.lower()

def calculate_character_accuracy(reference_text, extracted_text):
    """Calculate character-level accuracy using edit distance."""
    ref_normalized = normalize_text(reference_text)
    ext_normalized = normalize_text(extracted_text)

    # Calculate edit distance using difflib
    matcher = difflib.SequenceMatcher(None, ref_normalized, ext_normalized)
    similarity = matcher.ratio()
    return similarity * 100

def calculate_word_accuracy(reference_text, extracted_text):
    """Calculate word-level accuracy."""
    ref_words = normalize_text(reference_text).split()
    ext_words = normalize_text(extracted_text).split()

    # Calculate word-level similarity
    matcher = difflib.SequenceMatcher(None, ref_words, ext_words)
    similarity = matcher.ratio()
    return similarity * 100

def load_ground_truth(pdf_path):
    """Load ground truth text file if it exists."""
    # Look for a ground truth file with the same name but .gt.txt extension
    gt_path = pdf_path.with_suffix('.gt.txt')
    if gt_path.exists():
        return gt_path.read_text(encoding='utf-8')

    # Alternative: look for .truth.txt extension
    alt_gt_path = pdf_path.with_name(pdf_path.stem + '.truth.txt')
    if alt_gt_path.exists():
        return alt_gt_path.read_text(encoding='utf-8')

    return None

def main():
    input_doc_path = Path(r"C:\Users\<USER>\Documents\Intern_2025\ArunKumarV(2951795)\Data\data1.pdf").resolve()
    if not input_doc_path.exists():
        print(f"File not found: {input_doc_path}")
        return

    print("Starting OCR processing...")
    pipeline_options = PdfPipelineOptions(
        do_ocr=True,
        force_full_page_ocr=True,
        ocr_options=ocr_options
    )
    converter = DocumentConverter(
        format_options={
            InputFormat.PDF: PdfFormatOption(
                pipeline_options=pipeline_options,
            )
        }
    )
    result = converter.convert(input_doc_path)
    extracted_text = result.document.export_to_text()
    output_txt_path = input_doc_path.with_suffix(".txt")
    output_txt_path.write_text(extracted_text, encoding='utf-8')

    print(f"OCR text saved to: {output_txt_path}")

    # Try to load ground truth and calculate accuracy
    ground_truth = load_ground_truth(input_doc_path)

    if ground_truth:
        print("\n" + "="*50)
        print("ACCURACY ANALYSIS")
        print("="*50)

        char_accuracy = calculate_character_accuracy(ground_truth, extracted_text)
        word_accuracy = calculate_word_accuracy(ground_truth, extracted_text)

        print(f"Character-level accuracy: {char_accuracy:.2f}%")
        print(f"Word-level accuracy: {word_accuracy:.2f}%")

        # Additional statistics
        ref_chars = len(normalize_text(ground_truth))
        ext_chars = len(normalize_text(extracted_text))
        ref_words = len(normalize_text(ground_truth).split())
        ext_words = len(normalize_text(extracted_text).split())

        print(f"\nText Statistics:")
        print(f"Ground truth: {ref_chars} characters, {ref_words} words")
        print(f"Extracted text: {ext_chars} characters, {ext_words} words")
        print(f"Character difference: {abs(ref_chars - ext_chars)} ({abs(ref_chars - ext_chars)/ref_chars*100:.1f}%)")
        print(f"Word difference: {abs(ref_words - ext_words)} ({abs(ref_words - ext_words)/ref_words*100:.1f}%)")

    else:
        print("\n" + "="*50)
        print("NO GROUND TRUTH FOUND")
        print("="*50)
        print("To calculate accuracy, create a ground truth file:")
        print(f"  - {input_doc_path.with_suffix('.gt.txt')}")
        print(f"  - OR {input_doc_path.with_name(input_doc_path.stem + '.truth.txt')}")
        print("The ground truth file should contain the expected text content.")

        # Show basic text statistics
        ext_chars = len(normalize_text(extracted_text))
        ext_words = len(normalize_text(extracted_text).split())
        print(f"\nExtracted Text Statistics:")
        print(f"Characters: {ext_chars}")
        print(f"Words: {ext_words}")
        print(f"Lines: {len(extracted_text.splitlines())}")

if __name__ == "__main__":
    main()
