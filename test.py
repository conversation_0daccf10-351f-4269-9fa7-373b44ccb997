from pathlib import Path
import difflib
import re
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import (
    PdfPipelineOptions,
    TesseractCliOcrOptions,
)
from docling.document_converter import DocumentConverter, PdfFormatOption
ocr_options = TesseractCliOcrOptions(
    lang=["auto"],
    tesseract_cmd=r"C:\Users\<USER>\Documents\Intern_2025\ArunKumarV(2951795)\Tesseractengine\tesseract.exe"
)

def normalize_text(text):
    """Normalize text for comparison by removing extra whitespace and converting to lowercase."""
    text = re.sub(r'\s+', ' ', text.strip())
    return text.lower()

def calculate_character_accuracy(reference_text, extracted_text):
    """Calculate character-level accuracy using edit distance."""
    ref_normalized = normalize_text(reference_text)
    ext_normalized = normalize_text(extracted_text)
    matcher = difflib.SequenceMatcher(None, ref_normalized, ext_normalized)
    similarity = matcher.ratio()
    return similarity * 100

def calculate_word_accuracy(reference_text, extracted_text):
    """Calculate word-level accuracy."""
    ref_words = normalize_text(reference_text).split()
    ext_words = normalize_text(extracted_text).split()
    matcher = difflib.SequenceMatcher(None, ref_words, ext_words)
    similarity = matcher.ratio()
    return similarity * 100

def load_ground_truth(pdf_path):
    """Load ground truth text file if it exists."""
    gt_path = pdf_path.with_suffix('.gt.txt')
    if gt_path.exists():
        return gt_path.read_text(encoding='utf-8')
    alt_gt_path = pdf_path.with_name(pdf_path.stem + '.truth.txt')
    if alt_gt_path.exists():
        return alt_gt_path.read_text(encoding='utf-8')

    return None

def estimate_ocr_quality(text):
    """Estimate OCR quality based on text characteristics."""
    if not text.strip():
        return 0.0

    total_chars = len(text)

    readable_chars = len(re.findall(r'[a-zA-Z0-9\s.,!?;:\-\'"()]', text))

    words = text.split()
    likely_words = 0
    for word in words:
        clean_word = re.sub(r'[^a-zA-Z]', '', word.lower())
        if len(clean_word) >= 2 and any(vowel in clean_word for vowel in 'aeiou'):
            likely_words += 1

    char_quality = (readable_chars / total_chars) * 100 if total_chars > 0 else 0
    word_quality = (likely_words / len(words)) * 100 if words else 0

    estimated_quality = (char_quality + word_quality) / 2

    return min(estimated_quality, 95.0)

def main():
    
    possible_paths = [
        Path(r"C:\Users\<USER>\Documents\Intern_2025\ArunKumarV(2951795)\Data\data1.pdf"),
        Path("data1.pdf"),
        Path("Data/data1.pdf"),
        Path("sample.pdf")
    ]

    input_doc_path = None
    for path in possible_paths:
        if path.exists():
            input_doc_path = path.resolve()
            break

    if input_doc_path is None:
        print("No PDF file found. Tried the following locations:")
        for path in possible_paths:
            print(f"  - {path}")
        print("\nPlease ensure a PDF file exists in one of these locations.")
        return

    print(f"Processing PDF: {input_doc_path}")

    print("Starting OCR processing...")
    pipeline_options = PdfPipelineOptions(
        do_ocr=True,
        force_full_page_ocr=True,
        ocr_options=ocr_options
    )
    converter = DocumentConverter(
        format_options={
            InputFormat.PDF: PdfFormatOption(
                pipeline_options=pipeline_options,
            )
        }
    )
    result = converter.convert(input_doc_path)
    extracted_text = result.document.export_to_text()
    output_txt_path = input_doc_path.with_suffix(".txt")
    output_txt_path.write_text(extracted_text, encoding='utf-8')

    print(f"OCR text saved to: {output_txt_path}")
    ground_truth = load_ground_truth(input_doc_path)

    if ground_truth:
        print("\n" + "="*50)
        print("ACCURACY ANALYSIS")
        print("="*50)

        char_accuracy = calculate_character_accuracy(ground_truth, extracted_text)
        word_accuracy = calculate_word_accuracy(ground_truth, extracted_text)

        print(f"Character-level accuracy: {char_accuracy:.2f}%")
        print(f"Word-level accuracy: {word_accuracy:.2f}%")

        ref_chars = len(normalize_text(ground_truth))
        ext_chars = len(normalize_text(extracted_text))
        ref_words = len(normalize_text(ground_truth).split())
        ext_words = len(normalize_text(extracted_text).split())

        print(f"\nText Statistics:")
        print(f"Ground truth: {ref_chars} characters, {ref_words} words")
        print(f"Extracted text: {ext_chars} characters, {ext_words} words")
        print(f"Character difference: {abs(ref_chars - ext_chars)} ({abs(ref_chars - ext_chars)/ref_chars*100:.1f}%)")
        print(f"Word difference: {abs(ref_words - ext_words)} ({abs(ref_words - ext_words)/ref_words*100:.1f}%)")

    else:
        print("\n" + "="*50)
        print("ACCURACY ESTIMATION ")
        print("="*50)
        estimated_accuracy = estimate_ocr_quality(extracted_text)
        print(f"Estimated OCR Quality: {estimated_accuracy:.1f}%")
        print("(This is an estimation based on text patterns and readability)")
        ext_chars = len(normalize_text(extracted_text))
        ext_words = len(normalize_text(extracted_text).split())
        print(f"\nExtracted Text Statistics:")
        print(f"Characters: {ext_chars}")
        print(f"Words: {ext_words}")
        print(f"Lines: {len(extracted_text.splitlines())}")
        preview_text = extracted_text[:200] + "..." if len(extracted_text) > 200 else extracted_text
        print(f"\nText Preview:")
        print(f"'{preview_text}'")

if __name__ == "__main__":
    main()
