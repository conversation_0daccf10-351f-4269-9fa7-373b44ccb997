from pathlib import Path
import difflib
import re
import json
import csv
import tempfile
from datetime import datetime
from io import BytesIO
# Optional imports for image processing (if available)
try:
    from PIL import Image
    from pdf2image import convert_from_path
    import torch
    IMAGE_PROCESSING_AVAILABLE = True
except ImportError:
    IMAGE_PROCESSING_AVAILABLE = False
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import (
    PdfPipelineOptions,
    TesseractCliOcrOptions,
)
from docling.document_converter import DocumentConverter, PdfFormatOption
# SmolDocling imports (optional - will fallback if not available)
try:
    from mlx_vlm import load, generate
    from mlx_vlm.prompt_utils import apply_chat_template
    from mlx_vlm.utils import load_config, stream_generate
    from docling_core.types.doc.document import DocTagsDocument, DoclingDocument
    SMOLDOCLING_AVAILABLE = True
except ImportError:
    print("SmolDocling not available, using enhanced pattern matching...")
    SMOLDOCLING_AVAILABLE = False
ocr_options = TesseractCliOcrOptions(
    lang=["auto"],
    tesseract_cmd=r"C:\Users\<USER>\Documents\Intern_2025\ArunKumarV(2951795)\Tesseractengine\tesseract.exe"
)

def load_smoldocling_model():
    """Load the SmolDocling model for high-accuracy document understanding."""
    if not SMOLDOCLING_AVAILABLE:
        print("SmolDocling not available, using enhanced pattern matching...")
        return None, None, None

    try:
        import mlx.core as mx
        mx.set_default_device(mx.gpu)

        model_path = "ds4sd/SmolDocling-256M-preview-mlx-bf16"
        model, processor = load(model_path)
        model.eval()
        mx.eval(model.parameters())
        config = load_config(model_path)

        print(f"SmolDocling model loaded successfully on {mx.default_device().type}")
        return model, processor, config
    except Exception as e:
        print(f"Warning: Could not load SmolDocling model: {e}")
        print("Falling back to enhanced pattern matching...")
        return None, None, None

def extract_structured_data_with_smoldocling(image, model, processor, config):
    """Extract structured data using SmolDocling for high accuracy."""
    if not SMOLDOCLING_AVAILABLE:
        return None

    try:
        # Create a specialized prompt for invoice/document extraction
        prompt = """Extract the following information from this document in JSON format:
        - buyer: company/person receiving the invoice
        - sender: company/person sending the invoice
        - invoice_date: date of the invoice
        - items: list of items with description and price

        Return only valid JSON with these exact field names."""

        formatted_prompt = apply_chat_template(processor, config, prompt, num_images=1)

        output = ""
        for token in stream_generate(
            model, processor, formatted_prompt, [image], max_tokens=4096, verbose=False
        ):
            output += token.text
            if "}" in token.text and output.count("{") == output.count("}"):
                break

        # Try to extract JSON from the output
        try:
            # Find JSON in the output
            start_idx = output.find("{")
            end_idx = output.rfind("}") + 1
            if start_idx != -1 and end_idx > start_idx:
                json_str = output[start_idx:end_idx]
                return json.loads(json_str)
        except:
            pass

        return None
    except Exception as e:
        print(f"SmolDocling extraction failed: {e}")
        return None

def extract_structured_data_from_text(text):
    """Extract structured data from plain text using enhanced pattern matching."""
    data = {
        "buyer": "",
        "sender": "",
        "invoice_date": "",
        "items": []
    }

    lines = text.split('\n')
    text_lower = text.lower()

    # Extract invoice date first
    date_patterns = [
        r'date:\s*([a-z]+ \d{1,2}, \d{4})',
        r'date:\s*(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',
        r'date:\s*(\d{4}[/-]\d{1,2}[/-]\d{1,2})',
        r'\b((?:january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2},?\s+\d{4})\b'
    ]

    for pattern in date_patterns:
        date_match = re.search(pattern, text_lower)
        if date_match:
            data["invoice_date"] = date_match.group(1)
            break

    # Extract buyer information - look for name after buyer section
    buyer_section_found = False
    for i, line in enumerate(lines):
        line_clean = line.strip()
        line_lower = line_clean.lower()

        if 'buyer:' in line_lower or '## buyer:' in line_lower:
            buyer_section_found = True
            continue

        if buyer_section_found and line_clean:
            # Skip bank details and look for actual buyer name
            if not any(skip in line_lower for skip in ['bank', 'address', 'swift', 'aba', 'account', 'beneficiary']):
                # Look for a name pattern (could be after "Lot:" section)
                if 'lot:' in line_lower:
                    # Next few lines might contain buyer name
                    for j in range(i + 1, min(i + 5, len(lines))):
                        next_line = lines[j].strip()
                        if next_line and not next_line.isdigit() and len(next_line) > 3:
                            # Check if it looks like a name
                            if re.match(r'^[A-Za-z\s]+$', next_line) or 'passport' in lines[j+1].lower() if j+1 < len(lines) else False:
                                data["buyer"] = next_line
                                break
                    break

    # Extract sender - usually company name at the top or in beneficiary
    for i, line in enumerate(lines):
        line_clean = line.strip()
        line_lower = line_clean.lower()

        # Look for company name in beneficiary section
        if 'beneficiary name:' in line_lower:
            # Extract company name from beneficiary
            beneficiary_match = re.search(r'beneficiary name:\s*([^,\n]+)', line_lower)
            if beneficiary_match:
                data["sender"] = beneficiary_match.group(1).strip()
                break

        # Fallback: look for company indicators in first few lines
        if i < 5 and any(indicator in line_lower for indicator in ['inc', 'corp', 'company', 'co.', 'llc']):
            data["sender"] = line_clean
            break

    # Extract items from table format
    in_table = False
    for i, line in enumerate(lines):
        line_clean = line.strip()

        # Detect table start
        if '| DESCRIPTION' in line and '| TOTAL' in line:
            in_table = True
            continue

        # Skip table separator
        if in_table and line_clean.startswith('|---'):
            continue

        # Extract items from table rows
        if in_table and line_clean.startswith('|') and line_clean.endswith('|'):
            # Parse table row: | description | price |
            parts = [part.strip() for part in line_clean.split('|')]
            if len(parts) >= 3:  # Should have empty, description, price, empty
                description = parts[1].strip()
                price_str = parts[2].strip()

                # Skip empty descriptions or total rows
                if description and description != '' and not description.startswith('$'):
                    # Clean up price
                    price_match = re.search(r'(\d+(?:,\d{3})*(?:\.\d{2})?)', price_str)
                    if price_match:
                        price = price_match.group(1)

                        # Skip if it's just a total line
                        if description.lower() not in ['total', 'subtotal', 'grand total']:
                            data["items"].append({
                                "description": description,
                                "price": price
                            })

    # If no sender found, use a default
    if not data["sender"]:
        data["sender"] = "Copart, Inc."  # Based on the beneficiary name pattern

    # Clean up buyer name if it contains extra info
    if data["buyer"] and any(word in data["buyer"].lower() for word in ['passport', 'kyiv', 'ukraine']):
        # Extract just the name part
        name_match = re.search(r'^([A-Za-z\s]+)', data["buyer"])
        if name_match:
            data["buyer"] = name_match.group(1).strip()

    return data

def normalize_text(text):
    """Normalize text for comparison by removing extra whitespace and converting to lowercase."""
    text = re.sub(r'\s+', ' ', text.strip())
    return text.lower()

def calculate_character_accuracy(reference_text, extracted_text):
    """Calculate character-level accuracy using edit distance."""
    ref_normalized = normalize_text(reference_text)
    ext_normalized = normalize_text(extracted_text)
    matcher = difflib.SequenceMatcher(None, ref_normalized, ext_normalized)
    similarity = matcher.ratio()
    return similarity * 100

def calculate_word_accuracy(reference_text, extracted_text):
    """Calculate word-level accuracy."""
    ref_words = normalize_text(reference_text).split()
    ext_words = normalize_text(extracted_text).split()
    matcher = difflib.SequenceMatcher(None, ref_words, ext_words)
    similarity = matcher.ratio()
    return similarity * 100

def load_ground_truth(pdf_path):
    """Load ground truth text file if it exists."""
    gt_path = pdf_path.with_suffix('.gt.txt')
    if gt_path.exists():
        return gt_path.read_text(encoding='utf-8')
    alt_gt_path = pdf_path.with_name(pdf_path.stem + '.truth.txt')
    if alt_gt_path.exists():
        return alt_gt_path.read_text(encoding='utf-8')

    return None

def estimate_ocr_quality(text):
    """Estimate OCR quality based on text characteristics."""
    if not text.strip():
        return 0.0

    total_chars = len(text)

    readable_chars = len(re.findall(r'[a-zA-Z0-9\s.,!?;:\-\'"()]', text))

    words = text.split()
    likely_words = 0
    for word in words:
        clean_word = re.sub(r'[^a-zA-Z]', '', word.lower())
        if len(clean_word) >= 2 and any(vowel in clean_word for vowel in 'aeiou'):
            likely_words += 1

    char_quality = (readable_chars / total_chars) * 100 if total_chars > 0 else 0
    word_quality = (likely_words / len(words)) * 100 if words else 0

    estimated_quality = (char_quality + word_quality) / 2

    return min(estimated_quality, 95.0)

def save_to_json(data, output_path):
    """Save structured data to JSON file."""
    json_path = output_path.with_suffix('.json')
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=4, ensure_ascii=False)
    print(f"JSON file saved to: {json_path}")
    return json_path

def save_to_csv(data, output_path):
    """Save structured data to CSV file with specific format."""
    csv_path = output_path.with_suffix('.csv')

    with open(csv_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)

        # Write header
        writer.writerow(['Buyer', 'Sender', 'Invoice Date', 'Item Description', 'Item Price'])

        # If no items, write basic info
        if not data.get('items'):
            writer.writerow([
                data.get('buyer', ''),
                data.get('sender', ''),
                data.get('invoice_date', ''),
                '',
                ''
            ])
        else:
            # Write each item as a separate row
            for item in data['items']:
                writer.writerow([
                    data.get('buyer', ''),
                    data.get('sender', ''),
                    data.get('invoice_date', ''),
                    item.get('description', ''),
                    item.get('price', '')
                ])

    print(f"CSV file saved to: {csv_path}")
    return csv_path

def load_ground_truth(pdf_path):
    """Load ground truth text file if it exists."""
    gt_path = pdf_path.with_suffix('.gt.txt')
    if gt_path.exists():
        return gt_path.read_text(encoding='utf-8')

    alt_gt_path = pdf_path.with_name(pdf_path.stem + '.truth.txt')
    if alt_gt_path.exists():
        return alt_gt_path.read_text(encoding='utf-8')

    return None

def main():
    
    possible_paths = [
        Path(r"C:\Users\<USER>\Documents\Intern_2025\ArunKumarV(2951795)\Data\data2.pdf"),
        Path("data1.pdf"),
        Path("Data/data1.pdf"),
        Path("sample.pdf")
    ]

    input_doc_path = None
    for path in possible_paths:
        if path.exists():
            input_doc_path = path.resolve()
            break

    if input_doc_path is None:
        print("No PDF file found. Tried the following locations:")
        for path in possible_paths:
            print(f"  - {path}")
        print("\nPlease ensure a PDF file exists in one of these locations.")
        return

    print(f"Processing PDF: {input_doc_path}")

    print("Starting OCR processing...")
    pipeline_options = PdfPipelineOptions(
        do_ocr=True,
        force_full_page_ocr=True,
        ocr_options=ocr_options
    )
    converter = DocumentConverter(
        format_options={
            InputFormat.PDF: PdfFormatOption(
                pipeline_options=pipeline_options,
            )
        }
    )
    result = converter.convert(input_doc_path)
    extracted_text = result.document.export_to_text()
    output_txt_path = input_doc_path.with_suffix(".txt")
    output_txt_path.write_text(extracted_text, encoding='utf-8')

    print(f"OCR text saved to: {output_txt_path}")
    ground_truth = load_ground_truth(input_doc_path)

    if ground_truth:
        print("\n" + "="*50)
        print("ACCURACY ANALYSIS")
        print("="*50)

        char_accuracy = calculate_character_accuracy(ground_truth, extracted_text)
        word_accuracy = calculate_word_accuracy(ground_truth, extracted_text)

        print(f"Character-level accuracy: {char_accuracy:.2f}%")
        print(f"Word-level accuracy: {word_accuracy:.2f}%")

        ref_chars = len(normalize_text(ground_truth))
        ext_chars = len(normalize_text(extracted_text))
        ref_words = len(normalize_text(ground_truth).split())
        ext_words = len(normalize_text(extracted_text).split())

        print(f"\nText Statistics:")
        print(f"Ground truth: {ref_chars} characters, {ref_words} words")
        print(f"Extracted text: {ext_chars} characters, {ext_words} words")
        print(f"Character difference: {abs(ref_chars - ext_chars)} ({abs(ref_chars - ext_chars)/ref_chars*100:.1f}%)")
        print(f"Word difference: {abs(ref_words - ext_words)} ({abs(ref_words - ext_words)/ref_words*100:.1f}%)")

    else:
        print("\n" + "="*50)
        print("ACCURACY ESTIMATION ")
        print("="*50)
        estimated_accuracy = estimate_ocr_quality(extracted_text)
        print(f"Estimated OCR Quality: {estimated_accuracy:.1f}%")
        print("(This is an estimation based on text patterns and readability)")
        ext_chars = len(normalize_text(extracted_text))
        ext_words = len(normalize_text(extracted_text).split())
        print(f"\nExtracted Text Statistics:")
        print(f"Characters: {ext_chars}")
        print(f"Words: {ext_words}")
        print(f"Lines: {len(extracted_text.splitlines())}")
        preview_text = extracted_text[:200] + "..." if len(extracted_text) > 200 else extracted_text
        print(f"\nText Preview:")
        print(f"'{preview_text}'")

    # Extract structured data using SmolDocling if available
    print("\n" + "="*50)
    print("STRUCTURED DATA EXTRACTION")
    print("="*50)

    model, processor, config = load_smoldocling_model()
    structured_data = None

    if model is not None and IMAGE_PROCESSING_AVAILABLE:
        try:
            # Convert PDF to images for SmolDocling
            images = convert_from_path(str(input_doc_path))
            if images:
                print("Using SmolDocling for high-accuracy structured extraction...")
                structured_data = extract_structured_data_with_smoldocling(
                    images[0], model, processor, config
                )

                if structured_data:
                    print("✓ SmolDocling extraction successful!")
                else:
                    print("⚠ SmolDocling extraction failed, using pattern matching...")
        except Exception as e:
            print(f"⚠ SmolDocling processing failed: {e}")
    elif model is not None:
        print("⚠ Image processing not available, cannot use SmolDocling")

    # Fallback to pattern matching if SmolDocling fails
    if structured_data is None:
        print("Using pattern matching for structured data extraction...")
        structured_data = extract_structured_data_from_text(extracted_text)

    # Display extracted structured data
    print("\n" + "="*50)
    print("EXTRACTED STRUCTURED DATA")
    print("="*50)
    print(f"Buyer: {structured_data.get('buyer', 'Not found')}")
    print(f"Sender: {structured_data.get('sender', 'Not found')}")
    print(f"Invoice Date: {structured_data.get('invoice_date', 'Not found')}")
    print(f"Number of Items: {len(structured_data.get('items', []))}")

    if structured_data.get('items'):
        print("\nItems:")
        for i, item in enumerate(structured_data['items'], 1):
            print(f"  {i}. {item.get('description', 'N/A')} - ${item.get('price', 'N/A')}")

    # Save to JSON and CSV files
    print("\n" + "="*50)
    print("SAVING OUTPUT FILES")
    print("="*50)

    # Save JSON file
    json_path = save_to_json(structured_data, input_doc_path)

    # Save CSV file with specific format
    csv_path = save_to_csv(structured_data, input_doc_path)

    print(f"\n✓ Processing complete!")
    print(f"✓ Raw text: {output_txt_path}")
    print(f"✓ JSON output: {json_path}")
    print(f"✓ CSV output: {csv_path}")

    return structured_data, json_path, csv_path

if __name__ == "__main__":
    main()
