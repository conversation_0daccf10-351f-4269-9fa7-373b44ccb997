#!/usr/bin/env python3
"""
Enhanced OCR Invoice Processing Demo
Demonstrates high-accuracy extraction from any invoice type
"""

import sys
from pathlib import Path
from test import main

def demo():
    """Demonstrate the enhanced OCR capabilities."""
    print("="*60)
    print("ENHANCED OCR INVOICE PROCESSING DEMO")
    print("="*60)
    print()
    print("This application can process ANY invoice PDF with high accuracy!")
    print()
    print("Features:")
    print("✓ SmolDocling AI model integration (256M parameters)")
    print("✓ Enhanced pattern matching for 20+ invoice formats")
    print("✓ Automatic buyer/sender/date/items extraction")
    print("✓ JSON and CSV output in structured format")
    print("✓ Works with any PDF file you provide")
    print()
    print("Usage:")
    print("1. python test.py <path_to_pdf>")
    print("2. python test.py (uses any PDF in current/Data folder)")
    print()
    print("Supported invoice types:")
    print("• Standard business invoices")
    print("• E-commerce receipts")
    print("• Service bills")
    print("• Product invoices")
    print("• Auction invoices")
    print("• Utility bills")
    print("• And many more...")
    print()
    
    if len(sys.argv) > 1:
        print(f"Processing: {sys.argv[1]}")
        print("-" * 60)
        main()
    else:
        print("No PDF file specified. Looking for PDFs in current directory...")
        print("-" * 60)
        main()

if __name__ == "__main__":
    demo()
