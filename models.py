"""
Pydantic models for invoice data validation and normalization.
Supports various document types including printed invoices, digital receipts, and handwritten documents.
"""

from datetime import datetime, date
from decimal import Decimal
from typing import List, Optional, Union, Any
from pydantic import BaseModel, Field, field_validator, model_validator
import re


class LineItem(BaseModel):
    """Individual line item in an invoice."""
    name: str = Field(..., description="Item name or description", min_length=1)
    quantity: Optional[Union[int, float]] = Field(default=1, description="Quantity of items", ge=0)
    unit_price: Optional[float] = Field(default=None, description="Price per unit", ge=0)
    total_price: float = Field(..., description="Total price for this line item", ge=0)
    category: Optional[str] = Field(default=None, description="Item category")
    
    @field_validator('name')
    @classmethod
    def clean_name(cls, v):
        """Clean and normalize item name."""
        if not v or not v.strip():
            raise ValueError("Item name cannot be empty")
        # Remove extra whitespace and normalize
        cleaned = re.sub(r'\s+', ' ', v.strip())
        # Remove common OCR artifacts
        cleaned = re.sub(r'[^\w\s\-\.\,\(\)\&]', '', cleaned)
        return cleaned.title()

    @field_validator('total_price')
    @classmethod
    def validate_total_price(cls, v):
        """Ensure total price is valid."""
        if v is None or v < 0:
            raise ValueError("Total price must be non-negative")
        return round(float(v), 2)

    @model_validator(mode='after')
    def validate_pricing(self):
        """Validate pricing consistency."""
        quantity = self.quantity or 1
        unit_price = self.unit_price
        total_price = self.total_price

        if unit_price is not None and quantity is not None and total_price is not None:
            expected_total = round(float(unit_price) * float(quantity), 2)
            actual_total = round(float(total_price), 2)
            # Allow small rounding differences
            if abs(expected_total - actual_total) > 0.02:
                # If there's a significant difference, trust the total_price
                self.unit_price = round(total_price / quantity, 2) if quantity > 0 else total_price

        return self


class TaxInfo(BaseModel):
    """Tax information for the invoice."""
    tax_type: Optional[str] = Field(default=None, description="Type of tax (VAT, GST, Sales Tax, etc.)")
    tax_rate: Optional[float] = Field(default=None, description="Tax rate as percentage", ge=0, le=100)
    tax_amount: Optional[float] = Field(default=None, description="Tax amount", ge=0)


class ContactInfo(BaseModel):
    """Contact information for vendor or customer."""
    name: Optional[str] = Field(default=None, description="Name of person or company")
    address: Optional[str] = Field(default=None, description="Address")
    phone: Optional[str] = Field(default=None, description="Phone number")
    email: Optional[str] = Field(default=None, description="Email address")
    
    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        """Basic email validation."""
        if v and '@' not in v:
            return None  # Invalid email, set to None
        return v

    @field_validator('phone')
    @classmethod
    def clean_phone(cls, v):
        """Clean phone number."""
        if v:
            # Remove non-digit characters except + and spaces
            cleaned = re.sub(r'[^\d\+\s\-\(\)]', '', v)
            return cleaned.strip() if cleaned else None
        return v


class InvoiceData(BaseModel):
    """Complete invoice data structure with validation."""
    
    # Basic invoice information
    invoice_number: Optional[str] = Field(default=None, description="Invoice number")
    invoice_date: Optional[Union[datetime, date, str]] = Field(default=None, description="Invoice date")
    due_date: Optional[Union[datetime, date, str]] = Field(default=None, description="Payment due date")
    
    # Parties involved
    vendor: Optional[ContactInfo] = Field(default=None, description="Vendor/seller information")
    customer: Optional[ContactInfo] = Field(default=None, description="Customer/buyer information")
    
    # Financial information
    subtotal: Optional[float] = Field(default=None, description="Subtotal before tax", ge=0)
    tax_info: Optional[TaxInfo] = Field(default=None, description="Tax information")
    total: float = Field(..., description="Total amount", ge=0)
    currency: Optional[str] = Field(default="USD", description="Currency code")
    
    # Line items
    items: List[LineItem] = Field(default_factory=list, description="List of line items")
    
    # Document metadata
    document_type: Optional[str] = Field(default="invoice", description="Type of document")
    confidence_score: Optional[float] = Field(default=None, description="Extraction confidence", ge=0, le=1)
    
    @field_validator('invoice_date', 'due_date', mode='before')
    @classmethod
    def parse_date(cls, v):
        """Parse various date formats."""
        if v is None:
            return None

        if isinstance(v, (datetime, date)):
            return v

        if isinstance(v, str):
            # Try various date formats
            date_formats = [
                '%Y-%m-%d',
                '%m/%d/%Y',
                '%d/%m/%Y',
                '%B %d, %Y',
                '%b %d, %Y',
                '%d %B %Y',
                '%d %b %Y',
                '%Y-%m-%d %H:%M:%S',
                '%m-%d-%Y',
                '%d-%m-%Y'
            ]

            # Clean the date string
            v = re.sub(r'[^\w\s\-\/\,\:]', '', v.strip())

            for fmt in date_formats:
                try:
                    return datetime.strptime(v, fmt).date()
                except ValueError:
                    continue

            # If no format matches, try to extract date components
            date_match = re.search(r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2,4})', v)
            if date_match:
                month, day, year = date_match.groups()
                year = int(year)
                if year < 100:
                    year += 2000 if year < 50 else 1900
                try:
                    return date(year, int(month), int(day))
                except ValueError:
                    # Try day/month instead
                    try:
                        return date(year, int(day), int(month))
                    except ValueError:
                        pass

        return None
    
    @field_validator('total')
    @classmethod
    def validate_total(cls, v):
        """Validate total amount."""
        if v is None or v < 0:
            raise ValueError("Total amount must be non-negative")
        return round(float(v), 2)

    @field_validator('currency')
    @classmethod
    def validate_currency(cls, v):
        """Validate currency code."""
        if v:
            v = v.upper().strip()
            # Common currency codes
            valid_currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY', 'INR']
            if v in valid_currencies:
                return v
        return 'USD'  # Default to USD

    @model_validator(mode='after')
    def validate_financial_consistency(self):
        """Validate financial calculations."""
        items = self.items or []
        total = self.total
        subtotal = self.subtotal
        tax_info = self.tax_info

        if items:
            # Calculate subtotal from items
            calculated_subtotal = sum(item.total_price for item in items)

            if subtotal is None:
                self.subtotal = round(calculated_subtotal, 2)

            # Validate total against subtotal + tax
            if tax_info and tax_info.tax_amount:
                expected_total = calculated_subtotal + tax_info.tax_amount
                if abs(expected_total - total) > 0.02:
                    # Adjust tax amount to match total
                    tax_info.tax_amount = round(total - calculated_subtotal, 2)

        return self
    
    def to_csv_rows(self) -> List[dict]:
        """Convert to CSV-friendly format."""
        base_info = {
            'invoice_number': self.invoice_number or '',
            'invoice_date': str(self.invoice_date) if self.invoice_date else '',
            'vendor_name': self.vendor.name if self.vendor else '',
            'customer_name': self.customer.name if self.customer else '',
            'currency': self.currency,
            'subtotal': self.subtotal or 0,
            'tax_amount': self.tax_info.tax_amount if self.tax_info else 0,
            'total': self.total
        }
        
        if not self.items:
            # Return single row with invoice info only
            return [{**base_info, 'item_name': '', 'quantity': 0, 'unit_price': 0, 'item_total': 0}]
        
        # Return one row per item
        rows = []
        for item in self.items:
            row = {
                **base_info,
                'item_name': item.name,
                'quantity': item.quantity or 1,
                'unit_price': item.unit_price or 0,
                'item_total': item.total_price
            }
            rows.append(row)
        
        return rows


class DocumentParsingResult(BaseModel):
    """Result of document parsing operation."""
    success: bool = Field(..., description="Whether parsing was successful")
    invoice_data: Optional[InvoiceData] = Field(default=None, description="Parsed invoice data")
    error_message: Optional[str] = Field(default=None, description="Error message if parsing failed")
    raw_text: Optional[str] = Field(default=None, description="Raw extracted text")
    processing_time: Optional[float] = Field(default=None, description="Processing time in seconds")
    extraction_method: Optional[str] = Field(default=None, description="Method used for extraction")
