
import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Any
import time
import json
from concurrent.futures import ThreadPoolExecutor, as_completed


try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("python-dotenv not installed. Install with: pip install python-dotenv")
    print("Or set GROQ_API_KEY as system environment variable")

from parser import DocumentParser
from models import DocumentParsingResult


logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class BatchProcessor:
    
    def __init__(self, groq_api_key: str, max_workers: int = 3):
        self.parser = DocumentParser(groq_api_key)
        self.max_workers = max_workers
    
    def find_documents(self, directory: Path, recursive: bool = True) -> List[Path]:
        supported_extensions = {'.pdf', '.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.txt'}
        documents = []
        
        if recursive:
            for ext in supported_extensions:
                documents.extend(directory.rglob(f"*{ext}"))
                documents.extend(directory.rglob(f"*{ext.upper()}"))
        else:
            for ext in supported_extensions:
                documents.extend(directory.glob(f"*{ext}"))
                documents.extend(directory.glob(f"*{ext.upper()}"))
        
        return sorted(list(set(documents)))
    
    def process_single_file(self, file_path: Path) -> Dict[str, Any]:
        start_time = time.time()
        result = self.parser.parse_document(file_path)
        processing_time = time.time() - start_time
        
        return {
            'file_path': str(file_path),
            'file_size': file_path.stat().st_size,
            'result': result,
            'processing_time': processing_time
        }
    
    def process_batch_sequential(self, file_paths: List[Path]) -> List[Dict[str, Any]]:
        """Process files sequentially."""
        results = []
        total_files = len(file_paths)
        
        logger.info(f"Processing {total_files} files sequentially...")
        
        for i, file_path in enumerate(file_paths, 1):
            logger.info(f"Processing {i}/{total_files}: {file_path.name}")
            result = self.process_single_file(file_path)
            results.append(result)
            
            if result['result'].success:
                logger.info(f"✓ Successfully processed {file_path.name}")
            else:
                logger.warning(f"✗ Failed to process {file_path.name}: {result['result'].error_message}")
        
        return results
    
    def process_batch_parallel(self, file_paths: List[Path]) -> List[Dict[str, Any]]:
        """Process files in parallel."""
        results = []
        total_files = len(file_paths)
        
        logger.info(f"Processing {total_files} files in parallel (max workers: {self.max_workers})...")
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            
            future_to_file = {
                executor.submit(self.process_single_file, file_path): file_path 
                for file_path in file_paths
            }
            
            
            completed = 0
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                completed += 1
                
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result['result'].success:
                        logger.info(f"✓ ({completed}/{total_files}) Successfully processed {file_path.name}")
                    else:
                        logger.warning(f"✗ ({completed}/{total_files}) Failed to process {file_path.name}: {result['result'].error_message}")
                
                except Exception as e:
                    logger.error(f"✗ ({completed}/{total_files}) Exception processing {file_path.name}: {e}")
                    results.append({
                        'file_path': str(file_path),
                        'file_size': file_path.stat().st_size if file_path.exists() else 0,
                        'result': DocumentParsingResult(
                            success=False,
                            error_message=str(e)
                        ),
                        'processing_time': 0
                    })
        
        
        results.sort(key=lambda x: x['file_path'])
        return results
    
    def generate_batch_report(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate a comprehensive batch processing report."""
        total_files = len(results)
        successful = sum(1 for r in results if r['result'].success)
        failed = total_files - successful
        
        total_processing_time = sum(r['processing_time'] for r in results)
        total_file_size = sum(r['file_size'] for r in results)
        
        successful_results = [r for r in results if r['result'].success]
        if successful_results:
            avg_processing_time = sum(r['processing_time'] for r in successful_results) / len(successful_results)
            total_invoices_processed = sum(
                len(r['result'].invoice_data.items) if r['result'].invoice_data else 0 
                for r in successful_results
            )
        else:
            avg_processing_time = 0
            total_invoices_processed = 0
        
        report = {
            'summary': {
                'total_files': total_files,
                'successful': successful,
                'failed': failed,
                'success_rate': (successful / total_files * 100) if total_files > 0 else 0,
                'total_processing_time': total_processing_time,
                'average_processing_time': avg_processing_time,
                'total_file_size_mb': total_file_size / (1024 * 1024),
                'total_invoices_processed': total_invoices_processed
            },
            'failed_files': [
                {
                    'file_path': r['file_path'],
                    'error': r['result'].error_message
                }
                for r in results if not r['result'].success
            ],
            'processing_stats': [
                {
                    'file_path': r['file_path'],
                    'file_size_kb': r['file_size'] / 1024,
                    'processing_time': r['processing_time'],
                    'success': r['result'].success,
                    'items_extracted': len(r['result'].invoice_data.items) if r['result'].success and r['result'].invoice_data else 0
                }
                for r in results
            ]
        }
        
        return report
    
    def save_batch_results(self, results: List[Dict[str, Any]], output_dir: Path) -> None:
        """Save batch processing results to files."""
        output_dir.mkdir(exist_ok=True)
        
        
        report = self.generate_batch_report(results)
        
        
        report_path = output_dir / "batch_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        
        csv_path = output_dir / "batch_results.csv"
        parsing_results = [r['result'] for r in results]
        self.parser.export_to_csv(parsing_results, csv_path)
        
        
        json_dir = output_dir / "individual_results"
        json_dir.mkdir(exist_ok=True)
        
        for result in results:
            if result['result'].success and result['result'].invoice_data:
                file_name = Path(result['file_path']).stem
                json_path = json_dir / f"{file_name}.json"
                
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(
                        result['result'].invoice_data.dict(),
                        f,
                        indent=2,
                        ensure_ascii=False,
                        default=str
                    )
        
        logger.info(f"Batch results saved to: {output_dir}")
        logger.info(f"Report: {report_path}")
        logger.info(f"CSV: {csv_path}")
        logger.info(f"Individual JSONs: {json_dir}")


def main():
    """Demo batch processing."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Batch Document Processor")
    parser.add_argument('--input-dir', required=True, help='Input directory containing documents')
    parser.add_argument('--output-dir', required=True, help='Output directory for results')
    parser.add_argument('--parallel', action='store_true', help='Use parallel processing')
    parser.add_argument('--max-workers', type=int, default=3, help='Maximum parallel workers')
    parser.add_argument('--recursive', action='store_true', help='Search subdirectories recursively')
    
    args = parser.parse_args()
    
    
    groq_api_key = os.getenv("GROQ_API_KEY")
    if not groq_api_key:
        logger.error("GROQ_API_KEY environment variable required")
        sys.exit(1)
    
    try:
        
        processor = BatchProcessor(groq_api_key, max_workers=args.max_workers)
        
        
        input_dir = Path(args.input_dir)
        documents = processor.find_documents(input_dir, recursive=args.recursive)
        
        if not documents:
            logger.error(f"No supported documents found in {input_dir}")
            sys.exit(1)
        
        logger.info(f"Found {len(documents)} documents to process")
        
        
        start_time = time.time()
        if args.parallel:
            results = processor.process_batch_parallel(documents)
        else:
            results = processor.process_batch_sequential(documents)
        
        total_time = time.time() - start_time
        
        
        output_dir = Path(args.output_dir)
        processor.save_batch_results(results, output_dir)
        
        
        report = processor.generate_batch_report(results)
        summary = report['summary']
        
        print(f"\n{'='*60}")
        print(f"BATCH PROCESSING COMPLETE")
        print(f"{'='*60}")
        print(f"Total files: {summary['total_files']}")
        print(f"Successful: {summary['successful']}")
        print(f"Failed: {summary['failed']}")
        print(f"Success rate: {summary['success_rate']:.1f}%")
        print(f"Total processing time: {total_time:.2f} seconds")
        print(f"Average time per file: {summary['average_processing_time']:.2f} seconds")
        print(f"Total invoices processed: {summary['total_invoices_processed']}")
        print(f"Results saved to: {output_dir}")
    
    except Exception as e:
        logger.error(f"Batch processing failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
